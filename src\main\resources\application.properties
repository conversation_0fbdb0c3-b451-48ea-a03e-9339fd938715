spring.application.name=huspark

# src/main/resources/application.properties
spring.datasource.url=********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=4865

spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Swagger (SpringDoc) path stays default: /swagger-ui.html

# JWT (use a 32+ char secret)
app.jwt.secret=change_this_to_a_long_random_secret_string_please_1234567890
app.jwt.expiry-seconds=86400

management.endpoints.web.exposure.include=health,info,metrics,env,threaddump,httpexchanges
management.endpoint.health.probes.enabled=true


