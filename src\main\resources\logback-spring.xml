<!-- src/main/resources/logback-spring.xml -->
<configuration>
    <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp />
                <pattern>
                    <pattern>
                        {
                        "level":"%level",
                        "logger":"%logger{36}",
                        "thread":"%thread",
                        "message":"%message",
                        "app":"library-api"
                        }
                    </pattern>
                </pattern>
                <arguments />
            </providers>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="JSON"/>
    </root>
</configuration>
